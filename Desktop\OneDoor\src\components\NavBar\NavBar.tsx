// components/NavBar/NavBar.jsx
import { useState } from 'react';
import ThemeToggle from "../ThemeToggle/ThemeToggle";
import logo from '../../assets/photo_2025-07-28_16-41-27-removebg-preview.png'
export default function NavBar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeLink, setActiveLink] = useState('home');

  const navLinks = [
    { id: 'home', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'services', label: 'Services' },
    { id: 'contact', label: 'Contact' }
  ];

  return (
    <nav className="px-6 lg:px-20 py-4 flex items-center justify-between fixed w-full top-0 left-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 z-50 transition-all duration-300">
      
      {/* الشعار */}
      <div className="flex items-center">
        <img src={logo} alt="" className=' w-10 h-10' />
        <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent dark:from-blue-400 dark:to-purple-400">
          One Door
        </h1>
      </div>

      {/* قائمة التنقل لسطح المكتب */}
      <div className="hidden md:flex items-center space-x-8">
        {navLinks.map((link) => (
          <a
            key={link.id}
            href={`#${link.id}`}
            className={`px-3 py-2 rounded-lg transition-all duration-200 font-medium ${
              activeLink === link.id
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
            }`}
            onClick={() => setActiveLink(link.id)}
          >
            {link.label}
          </a>
        ))}
        
        {/* زر تبديل الثيم */}
        <ThemeToggle />
      </div>

      {/* للجوال */}
      <div className="flex md:hidden items-center space-x-3">
        <ThemeToggle />
        
        <button 
          className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </button>
      </div>

      {/* القائمة المنسدلة للجوال */}
      {isMobileMenuOpen && (
        <div className="absolute top-full left-0 w-full md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-xl">
          <div className="px-6 py-4 space-y-2">
            {navLinks.map((link) => (
              <a
                key={link.id}
                href={`#${link.id}`}
                className={`block px-4 py-3 rounded-lg transition-all duration-200 font-medium ${
                  activeLink === link.id
                    ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
                onClick={() => {
                  setActiveLink(link.id);
                  setIsMobileMenuOpen(false);
                }}
              >
                {link.label}
              </a>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}