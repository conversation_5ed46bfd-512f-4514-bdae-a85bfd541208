
import { useEffect, useState } from 'react';

export default function Hero() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">

      {/* الخلفية المتحركة مع الأشكال المعينة */}
      <div className="absolute inset-0 overflow-hidden">
        {/* الأشكال المعينة الكبيرة */}
        <div
          className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-500 transform rotate-45 opacity-20 animate-pulse"
          style={{
            transform: `rotate(45deg) translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px)`,
            transition: 'transform 0.3s ease-out'
          }}
        />

        <div
          className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-purple-400 to-pink-500 transform rotate-45 opacity-30 animate-bounce"
          style={{
            transform: `rotate(45deg) translate(${-mousePosition.x * 0.05}px, ${mousePosition.y * 0.08}px)`,
            transition: 'transform 0.3s ease-out',
            animationDelay: '0.5s'
          }}
        />

        <div
          className="absolute bottom-32 left-1/4 w-20 h-20 bg-gradient-to-r from-green-400 to-blue-500 transform rotate-45 opacity-25 animate-pulse"
          style={{
            transform: `rotate(45deg) translate(${mousePosition.x * 0.08}px, ${-mousePosition.y * 0.06}px)`,
            transition: 'transform 0.3s ease-out',
            animationDelay: '1s'
          }}
        />

        <div
          className="absolute top-1/3 right-1/3 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 transform rotate-45 opacity-20 animate-bounce"
          style={{
            transform: `rotate(45deg) translate(${-mousePosition.x * 0.12}px, ${-mousePosition.y * 0.1}px)`,
            transition: 'transform 0.3s ease-out',
            animationDelay: '1.5s'
          }}
        />

        {/* الأشكال المعينة الصغيرة */}
        <div className="absolute top-1/4 left-1/2 w-8 h-8 bg-gradient-to-r from-indigo-400 to-purple-400 transform rotate-45 opacity-30 animate-ping" />
        <div className="absolute bottom-1/4 right-1/4 w-6 h-6 bg-gradient-to-r from-pink-400 to-red-400 transform rotate-45 opacity-25 animate-pulse" />
        <div className="absolute top-3/4 left-1/3 w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-400 transform rotate-45 opacity-20 animate-bounce" />

        {/* خطوط متحركة */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-300 to-transparent opacity-30 animate-pulse" />
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent opacity-20 animate-pulse" style={{ animationDelay: '2s' }} />
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="relative z-10 text-center px-6 max-w-6xl mx-auto">

        {/* العنوان الرئيسي */}
        <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-300 bg-clip-text text-transparent animate-fade-in">
          One Door
        </h1>

        {/* العنوان الفرعي */}
        <p className="text-xl md:text-2xl lg:text-3xl text-gray-700 dark:text-gray-300 mb-8 animate-slide-up font-light">
          Your Gateway to <span className="font-semibold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Digital Excellence</span>
        </p>

        {/* الوصف */}
        <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed animate-slide-up" style={{ animationDelay: '0.3s' }}>
          نحن نفتح لك الباب إلى عالم من الحلول الرقمية المبتكرة. من التطوير إلى التصميم، نحن شريكك في النجاح الرقمي.
        </p>

        {/* الأزرار */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style={{ animationDelay: '0.6s' }}>
          <button className="group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-2xl shadow-lg">
            <span className="flex items-center gap-2">
              ابدأ رحلتك
              <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </span>
          </button>

          <button className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-full hover:border-blue-500 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 transform hover:scale-105">
            تعرف علينا أكثر
          </button>
        </div>

        {/* إحصائيات */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 animate-fade-in" style={{ animationDelay: '0.9s' }}>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">100+</div>
            <div className="text-gray-600 dark:text-gray-400">مشروع مكتمل</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">50+</div>
            <div className="text-gray-600 dark:text-gray-400">عميل راضي</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-green-600 dark:text-green-400 mb-2">5+</div>
            <div className="text-gray-600 dark:text-gray-400">سنوات خبرة</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-orange-600 dark:text-orange-400 mb-2">24/7</div>
            <div className="text-gray-600 dark:text-gray-400">دعم فني</div>
          </div>
        </div>
      </div>

      {/* مؤشر التمرير */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg className="w-6 h-6 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </div>
  );
}
