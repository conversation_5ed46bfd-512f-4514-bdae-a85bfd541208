/* في ملف CSS الرئيسي */
@import "tailwindcss";

* {
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

html {
    scroll-behavior: smooth;
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    margin: 0;
    padding: 0;
    padding-top: 80px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Mode */
.light {
    color-scheme: light;
    background-color: #ffffff;
    color: #1f2937;
}

.light body {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1f2937;
}

/* Dark Mode */
.dark {
    color-scheme: dark;
    background-color: #0f172a;
    color: #f8fafc;
}

.dark body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #f8fafc;
}

/* تحسين الانتقالات للعناصر */
.dark *,
.light * {
    border-color: inherit;
}

/* منع الوميض أثناء التحميل */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
    }
}