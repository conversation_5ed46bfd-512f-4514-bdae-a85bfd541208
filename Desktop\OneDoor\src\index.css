/* في ملف CSS الرئيسي */
@import "tailwindcss";

* {
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

html {
    scroll-behavior: smooth;
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    margin: 0;
    padding: 0;
    padding-top: 80px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Mode */
.light {
    color-scheme: light;
    background-color: #ffffff;
    color: #1f2937;
}

.light body {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1f2937;
}

/* Dark Mode */
.dark {
    color-scheme: dark;
    background-color: #0f172a;
    color: #f8fafc;
}

.dark body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #f8fafc;
}

/* تحسين الانتقالات للعناصر */
.dark *,
.light * {
    border-color: inherit;
}

/* منع الوميض أثناء التحميل */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
    }
}

/* Hero Animations */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(45deg);
    }
    50% {
        transform: translateY(-20px) rotate(45deg);
    }
}

@keyframes rotate-slow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-fade-in {
    animation: fade-in 1s ease-out forwards;
}

.animate-slide-up {
    animation: slide-up 1s ease-out forwards;
    opacity: 0;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-rotate-slow {
    animation: rotate-slow 20s linear infinite;
}

/* تحسين الأداء للأنيميشن */
.animate-pulse,
.animate-bounce,
.animate-ping,
.animate-fade-in,
.animate-slide-up,
.animate-float {
    will-change: transform, opacity;
}

/* تأثيرات إضافية للأشكال */
.diamond-shape {
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .animate-slide-up {
        animation-duration: 0.8s;
    }

    .animate-fade-in {
        animation-duration: 0.8s;
    }
}